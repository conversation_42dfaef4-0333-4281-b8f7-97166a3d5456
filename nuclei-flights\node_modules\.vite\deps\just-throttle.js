import {
  __commonJS
} from "./chunk-OL3AADLO.js";

// node_modules/just-throttle/index.js
var require_just_throttle = __commonJS({
  "node_modules/just-throttle/index.js"(exports, module) {
    module.exports = throttle;
    function throttle(fn, interval, options) {
      var timeoutId = null;
      var throttledFn = null;
      var leading = options && options.leading;
      var trailing = options && options.trailing;
      if (leading == null) {
        leading = true;
      }
      if (trailing == null) {
        trailing = !leading;
      }
      if (leading == true) {
        trailing = false;
      }
      var cancel = function() {
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      };
      var flush = function() {
        var call = throttledFn;
        cancel();
        if (call) {
          call();
        }
      };
      var throttleWrapper = function() {
        var callNow = leading && !timeoutId;
        var context = this;
        var args = arguments;
        throttledFn = function() {
          return fn.apply(context, args);
        };
        if (!timeoutId) {
          timeoutId = setTimeout(function() {
            timeoutId = null;
            if (trailing) {
              return throttledFn();
            }
          }, interval);
        }
        if (callNow) {
          callNow = false;
          return throttledFn();
        }
      };
      throttleWrapper.cancel = cancel;
      throttleWrapper.flush = flush;
      return throttleWrapper;
    }
  }
});
export default require_just_throttle();
//# sourceMappingURL=just-throttle.js.map
