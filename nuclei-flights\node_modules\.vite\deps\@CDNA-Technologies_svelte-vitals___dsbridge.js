import {
  __commonJS
} from "./chunk-OL3AADLO.js";

// node_modules/dsbridge/index.js
var require_dsbridge = __commonJS({
  "node_modules/dsbridge/index.js"(exports, module) {
    var bridge = {
      default: exports,
      call: function(method, args, cb) {
        var ret = "";
        if (typeof args == "function") {
          cb = args;
          args = {};
        }
        var arg = { data: args === void 0 ? null : args };
        if (typeof cb == "function") {
          var cbName = "dscb" + window.dscb++;
          window[cbName] = cb;
          arg["_dscbstub"] = cbName;
        }
        arg = JSON.stringify(arg);
        if (window._dsbridge) {
          ret = _dsbridge.call(method, arg);
        } else if (window._dswk || navigator.userAgent.indexOf("_dsbridge") != -1) {
          ret = prompt("_dsbridge=" + method, arg);
        }
        return JSON.parse(ret || "{}").data;
      },
      register: function(name, fun, asyn) {
        var q = asyn ? window._dsaf : window._dsf;
        if (!window._dsInit) {
          window._dsInit = true;
          setTimeout(function() {
            bridge.call("_dsb.dsinit");
          }, 0);
        }
        if (typeof fun == "object") {
          q._obs[name] = fun;
        } else {
          q[name] = fun;
        }
      },
      registerAsyn: function(name, fun) {
        this.register(name, fun, true);
      },
      hasNativeMethod: function(name, type) {
        return this.call("_dsb.hasNativeMethod", { name, type: type || "all" });
      },
      disableJavascriptDialogBlock: function(disable) {
        this.call("_dsb.disableJavascriptDialogBlock", {
          disable: disable !== false
        });
      }
    };
    !function() {
      if (window._dsf)
        return;
      var ob = {
        _dsf: {
          _obs: {}
        },
        _dsaf: {
          _obs: {}
        },
        dscb: 0,
        dsBridge: bridge,
        close: function() {
          bridge.call("_dsb.closePage");
        },
        _handleMessageFromNative: function(info) {
          var arg = JSON.parse(info.data);
          var ret = {
            id: info.callbackId,
            complete: true
          };
          var f = this._dsf[info.method];
          var af = this._dsaf[info.method];
          var callSyn = function(f2, ob3) {
            ret.data = f2.apply(ob3, arg);
            bridge.call("_dsb.returnValue", ret);
          };
          var callAsyn = function(f2, ob3) {
            arg.push(function(data, complete) {
              ret.data = data;
              ret.complete = complete !== false;
              bridge.call("_dsb.returnValue", ret);
            });
            f2.apply(ob3, arg);
          };
          if (f) {
            callSyn(f, this._dsf);
          } else if (af) {
            callAsyn(af, this._dsaf);
          } else {
            var name = info.method.split(".");
            if (name.length < 2)
              return;
            var method = name.pop();
            var namespace = name.join(".");
            var obs = this._dsf._obs;
            var ob2 = obs[namespace] || {};
            var m = ob2[method];
            if (m && typeof m == "function") {
              callSyn(m, ob2);
              return;
            }
            obs = this._dsaf._obs;
            ob2 = obs[namespace] || {};
            m = ob2[method];
            if (m && typeof m == "function") {
              callAsyn(m, ob2);
              return;
            }
          }
        }
      };
      for (var attr in ob) {
        window[attr] = ob[attr];
      }
      bridge.register("_hasJavascriptMethod", function(method, tag) {
        var name = method.split(".");
        if (name.length < 2) {
          return !!(_dsf[name] || _dsaf[name]);
        } else {
          var method = name.pop();
          var namespace = name.join(".");
          var ob2 = _dsf._obs[namespace] || _dsaf._obs[namespace];
          return ob2 && !!ob2[method];
        }
      });
    }();
    module.exports = bridge;
  }
});
export default require_dsbridge();
//# sourceMappingURL=@CDNA-Technologies_svelte-vitals___dsbridge.js.map
