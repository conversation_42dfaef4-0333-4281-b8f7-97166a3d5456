{"version": 3, "sources": ["../../svelte/animate/index.mjs"], "sourcesContent": ["import { cubicOut } from '../easing/index.mjs';\nimport { is_function } from '../internal/index.mjs';\n\nfunction flip(node, { from, to }, params = {}) {\n    const style = getComputedStyle(node);\n    const transform = style.transform === 'none' ? '' : style.transform;\n    const [ox, oy] = style.transformOrigin.split(' ').map(parseFloat);\n    const dx = (from.left + from.width * ox / to.width) - (to.left + ox);\n    const dy = (from.top + from.height * oy / to.height) - (to.top + oy);\n    const { delay = 0, duration = (d) => Math.sqrt(d) * 120, easing = cubicOut } = params;\n    return {\n        delay,\n        duration: is_function(duration) ? duration(Math.sqrt(dx * dx + dy * dy)) : duration,\n        easing,\n        css: (t, u) => {\n            const x = u * dx;\n            const y = u * dy;\n            const sx = t + u * from.width / to.width;\n            const sy = t + u * from.height / to.height;\n            return `transform: ${transform} translate(${x}px, ${y}px) scale(${sx}, ${sy});`;\n        }\n    };\n}\n\nexport { flip };\n"], "mappings": ";;;;;;;;;AAGA,SAAS,KAAK,MAAM,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG;AAC3C,QAAM,QAAQ,iBAAiB,IAAI;AACnC,QAAM,YAAY,MAAM,cAAc,SAAS,KAAK,MAAM;AAC1D,QAAM,CAAC,IAAI,EAAE,IAAI,MAAM,gBAAgB,MAAM,GAAG,EAAE,IAAI,UAAU;AAChE,QAAM,KAAM,KAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,SAAU,GAAG,OAAO;AACjE,QAAM,KAAM,KAAK,MAAM,KAAK,SAAS,KAAK,GAAG,UAAW,GAAG,MAAM;AACjE,QAAM,EAAE,QAAQ,GAAG,WAAW,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,KAAK,SAAS,SAAS,IAAI;AAC/E,SAAO;AAAA,IACH;AAAA,IACA,UAAU,YAAY,QAAQ,IAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC,IAAI;AAAA,IAC3E;AAAA,IACA,KAAK,CAAC,GAAG,MAAM;AACX,YAAM,IAAI,IAAI;AACd,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI,IAAI,KAAK,QAAQ,GAAG;AACnC,YAAM,KAAK,IAAI,IAAI,KAAK,SAAS,GAAG;AACpC,aAAO,cAAc,uBAAuB,QAAQ,cAAc,OAAO;AAAA,IAC7E;AAAA,EACJ;AACJ;", "names": []}