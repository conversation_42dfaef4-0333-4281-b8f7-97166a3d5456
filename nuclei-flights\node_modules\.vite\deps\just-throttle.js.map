{"version": 3, "sources": ["../../just-throttle/index.js"], "sourcesContent": ["module.exports = throttle;\n\nfunction throttle(fn, interval, options) {\n  var timeoutId = null;\n  var throttledFn = null;\n  var leading = (options && options.leading);\n  var trailing = (options && options.trailing);\n\n  if (leading == null) {\n    leading = true; // default\n  }\n\n  if (trailing == null) {\n    trailing = !leading; //default\n  }\n\n  if (leading == true) {\n    trailing = false; // forced because there should be invocation per call\n  }\n\n  var cancel = function() {\n    if (timeoutId) {\n      clearTimeout(timeoutId);\n      timeoutId = null;\n    }\n  };\n\n  var flush = function() {\n    var call = throttledFn;\n    cancel();\n\n    if (call) {\n      call();\n    }\n  };\n\n  var throttleWrapper = function() {\n    var callNow = leading && !timeoutId;\n    var context = this;\n    var args = arguments;\n\n    throttledFn = function() {\n      return fn.apply(context, args);\n    };\n\n    if (!timeoutId) {\n      timeoutId = setTimeout(function() {\n        timeoutId = null;\n\n        if (trailing) {\n          return throttledFn();\n        }\n      }, interval);\n    }\n\n    if (callNow) {\n      callNow = false;\n      return throttledFn();\n    }\n  };\n\n  throttleWrapper.cancel = cancel;\n  throttleWrapper.flush = flush;\n\n  return throttleWrapper;\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,SAAS,IAAI,UAAU,SAAS;AACvC,UAAI,YAAY;AAChB,UAAI,cAAc;AAClB,UAAI,UAAW,WAAW,QAAQ;AAClC,UAAI,WAAY,WAAW,QAAQ;AAEnC,UAAI,WAAW,MAAM;AACnB,kBAAU;AAAA,MACZ;AAEA,UAAI,YAAY,MAAM;AACpB,mBAAW,CAAC;AAAA,MACd;AAEA,UAAI,WAAW,MAAM;AACnB,mBAAW;AAAA,MACb;AAEA,UAAI,SAAS,WAAW;AACtB,YAAI,WAAW;AACb,uBAAa,SAAS;AACtB,sBAAY;AAAA,QACd;AAAA,MACF;AAEA,UAAI,QAAQ,WAAW;AACrB,YAAI,OAAO;AACX,eAAO;AAEP,YAAI,MAAM;AACR,eAAK;AAAA,QACP;AAAA,MACF;AAEA,UAAI,kBAAkB,WAAW;AAC/B,YAAI,UAAU,WAAW,CAAC;AAC1B,YAAI,UAAU;AACd,YAAI,OAAO;AAEX,sBAAc,WAAW;AACvB,iBAAO,GAAG,MAAM,SAAS,IAAI;AAAA,QAC/B;AAEA,YAAI,CAAC,WAAW;AACd,sBAAY,WAAW,WAAW;AAChC,wBAAY;AAEZ,gBAAI,UAAU;AACZ,qBAAO,YAAY;AAAA,YACrB;AAAA,UACF,GAAG,QAAQ;AAAA,QACb;AAEA,YAAI,SAAS;AACX,oBAAU;AACV,iBAAO,YAAY;AAAA,QACrB;AAAA,MACF;AAEA,sBAAgB,SAAS;AACzB,sBAAgB,QAAQ;AAExB,aAAO;AAAA,IACT;AAAA;AAAA;", "names": []}