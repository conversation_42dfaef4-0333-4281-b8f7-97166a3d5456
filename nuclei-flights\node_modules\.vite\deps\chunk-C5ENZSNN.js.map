{"version": 3, "sources": ["../../svelte/store/index.mjs"], "sourcesContent": ["import { noop, safe_not_equal, subscribe, run_all, is_function } from '../internal/index.mjs';\nexport { get_store_value as get } from '../internal/index.mjs';\n\nconst subscriber_queue = [];\n/**\n * Creates a `Readable` store that allows reading by subscription.\n * @param value initial value\n * @param {StartStopNotifier} [start]\n */\nfunction readable(value, start) {\n    return {\n        subscribe: writable(value, start).subscribe\n    };\n}\n/**\n * Create a `Writable` store that allows both updating and reading by subscription.\n * @param {*=}value initial value\n * @param {StartStopNotifier=} start\n */\nfunction writable(value, start = noop) {\n    let stop;\n    const subscribers = new Set();\n    function set(new_value) {\n        if (safe_not_equal(value, new_value)) {\n            value = new_value;\n            if (stop) { // store is ready\n                const run_queue = !subscriber_queue.length;\n                for (const subscriber of subscribers) {\n                    subscriber[1]();\n                    subscriber_queue.push(subscriber, value);\n                }\n                if (run_queue) {\n                    for (let i = 0; i < subscriber_queue.length; i += 2) {\n                        subscriber_queue[i][0](subscriber_queue[i + 1]);\n                    }\n                    subscriber_queue.length = 0;\n                }\n            }\n        }\n    }\n    function update(fn) {\n        set(fn(value));\n    }\n    function subscribe(run, invalidate = noop) {\n        const subscriber = [run, invalidate];\n        subscribers.add(subscriber);\n        if (subscribers.size === 1) {\n            stop = start(set) || noop;\n        }\n        run(value);\n        return () => {\n            subscribers.delete(subscriber);\n            if (subscribers.size === 0 && stop) {\n                stop();\n                stop = null;\n            }\n        };\n    }\n    return { set, update, subscribe };\n}\nfunction derived(stores, fn, initial_value) {\n    const single = !Array.isArray(stores);\n    const stores_array = single\n        ? [stores]\n        : stores;\n    const auto = fn.length < 2;\n    return readable(initial_value, (set) => {\n        let started = false;\n        const values = [];\n        let pending = 0;\n        let cleanup = noop;\n        const sync = () => {\n            if (pending) {\n                return;\n            }\n            cleanup();\n            const result = fn(single ? values[0] : values, set);\n            if (auto) {\n                set(result);\n            }\n            else {\n                cleanup = is_function(result) ? result : noop;\n            }\n        };\n        const unsubscribers = stores_array.map((store, i) => subscribe(store, (value) => {\n            values[i] = value;\n            pending &= ~(1 << i);\n            if (started) {\n                sync();\n            }\n        }, () => {\n            pending |= (1 << i);\n        }));\n        started = true;\n        sync();\n        return function stop() {\n            run_all(unsubscribers);\n            cleanup();\n            // We need to set this to false because callbacks can still happen despite having unsubscribed:\n            // Callbacks might already be placed in the queue which doesn't know it should no longer\n            // invoke this derived store.\n            started = false;\n        };\n    });\n}\n/**\n * Takes a store and returns a new one derived from the old one that is readable.\n *\n * @param store - store to make readonly\n */\nfunction readonly(store) {\n    return {\n        subscribe: store.subscribe.bind(store)\n    };\n}\n\nexport { derived, readable, readonly, writable };\n"], "mappings": ";;;;;;;;;AAGA,IAAM,mBAAmB,CAAC;AAM1B,SAAS,SAAS,OAAO,OAAO;AAC5B,SAAO;AAAA,IACH,WAAW,SAAS,OAAO,KAAK,EAAE;AAAA,EACtC;AACJ;AAMA,SAAS,SAAS,OAAO,QAAQ,MAAM;AACnC,MAAI;AACJ,QAAM,cAAc,oBAAI,IAAI;AAC5B,WAAS,IAAI,WAAW;AACpB,QAAI,eAAe,OAAO,SAAS,GAAG;AAClC,cAAQ;AACR,UAAI,MAAM;AACN,cAAM,YAAY,CAAC,iBAAiB;AACpC,mBAAW,cAAc,aAAa;AAClC,qBAAW,GAAG;AACd,2BAAiB,KAAK,YAAY,KAAK;AAAA,QAC3C;AACA,YAAI,WAAW;AACX,mBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACjD,6BAAiB,GAAG,GAAG,iBAAiB,IAAI,EAAE;AAAA,UAClD;AACA,2BAAiB,SAAS;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,OAAO,IAAI;AAChB,QAAI,GAAG,KAAK,CAAC;AAAA,EACjB;AACA,WAASA,WAAU,KAAK,aAAa,MAAM;AACvC,UAAM,aAAa,CAAC,KAAK,UAAU;AACnC,gBAAY,IAAI,UAAU;AAC1B,QAAI,YAAY,SAAS,GAAG;AACxB,aAAO,MAAM,GAAG,KAAK;AAAA,IACzB;AACA,QAAI,KAAK;AACT,WAAO,MAAM;AACT,kBAAY,OAAO,UAAU;AAC7B,UAAI,YAAY,SAAS,KAAK,MAAM;AAChC,aAAK;AACL,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,KAAK,QAAQ,WAAAA,WAAU;AACpC;AACA,SAAS,QAAQ,QAAQ,IAAI,eAAe;AACxC,QAAM,SAAS,CAAC,MAAM,QAAQ,MAAM;AACpC,QAAM,eAAe,SACf,CAAC,MAAM,IACP;AACN,QAAM,OAAO,GAAG,SAAS;AACzB,SAAO,SAAS,eAAe,CAAC,QAAQ;AACpC,QAAI,UAAU;AACd,UAAM,SAAS,CAAC;AAChB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,UAAM,OAAO,MAAM;AACf,UAAI,SAAS;AACT;AAAA,MACJ;AACA,cAAQ;AACR,YAAM,SAAS,GAAG,SAAS,OAAO,KAAK,QAAQ,GAAG;AAClD,UAAI,MAAM;AACN,YAAI,MAAM;AAAA,MACd,OACK;AACD,kBAAU,YAAY,MAAM,IAAI,SAAS;AAAA,MAC7C;AAAA,IACJ;AACA,UAAM,gBAAgB,aAAa,IAAI,CAAC,OAAO,MAAM,UAAU,OAAO,CAAC,UAAU;AAC7E,aAAO,KAAK;AACZ,iBAAW,EAAE,KAAK;AAClB,UAAI,SAAS;AACT,aAAK;AAAA,MACT;AAAA,IACJ,GAAG,MAAM;AACL,iBAAY,KAAK;AAAA,IACrB,CAAC,CAAC;AACF,cAAU;AACV,SAAK;AACL,WAAO,SAAS,OAAO;AACnB,cAAQ,aAAa;AACrB,cAAQ;AAIR,gBAAU;AAAA,IACd;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,SAAS,OAAO;AACrB,SAAO;AAAA,IACH,WAAW,MAAM,UAAU,KAAK,KAAK;AAAA,EACzC;AACJ;", "names": ["subscribe"]}