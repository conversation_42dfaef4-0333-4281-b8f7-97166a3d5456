{"version": 3, "sources": ["../../svelte/easing/index.mjs"], "sourcesContent": ["export { identity as linear } from '../internal/index.mjs';\n\n/*\nAdapted from https://github.com/mattdesl\nDistributed under MIT License https://github.com/mattdesl/eases/blob/master/LICENSE.md\n*/\nfunction backInOut(t) {\n    const s = 1.70158 * 1.525;\n    if ((t *= 2) < 1)\n        return 0.5 * (t * t * ((s + 1) * t - s));\n    return 0.5 * ((t -= 2) * t * ((s + 1) * t + s) + 2);\n}\nfunction backIn(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n}\nfunction backOut(t) {\n    const s = 1.70158;\n    return --t * t * ((s + 1) * t + s) + 1;\n}\nfunction bounceOut(t) {\n    const a = 4.0 / 11.0;\n    const b = 8.0 / 11.0;\n    const c = 9.0 / 10.0;\n    const ca = 4356.0 / 361.0;\n    const cb = 35442.0 / 1805.0;\n    const cc = 16061.0 / 1805.0;\n    const t2 = t * t;\n    return t < a\n        ? 7.5625 * t2\n        : t < b\n            ? 9.075 * t2 - 9.9 * t + 3.4\n            : t < c\n                ? ca * t2 - cb * t + cc\n                : 10.8 * t * t - 20.52 * t + 10.72;\n}\nfunction bounceInOut(t) {\n    return t < 0.5\n        ? 0.5 * (1.0 - bounceOut(1.0 - t * 2.0))\n        : 0.5 * bounceOut(t * 2.0 - 1.0) + 0.5;\n}\nfunction bounceIn(t) {\n    return 1.0 - bounceOut(1.0 - t);\n}\nfunction circInOut(t) {\n    if ((t *= 2) < 1)\n        return -0.5 * (Math.sqrt(1 - t * t) - 1);\n    return 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1);\n}\nfunction circIn(t) {\n    return 1.0 - Math.sqrt(1.0 - t * t);\n}\nfunction circOut(t) {\n    return Math.sqrt(1 - --t * t);\n}\nfunction cubicInOut(t) {\n    return t < 0.5 ? 4.0 * t * t * t : 0.5 * Math.pow(2.0 * t - 2.0, 3.0) + 1.0;\n}\nfunction cubicIn(t) {\n    return t * t * t;\n}\nfunction cubicOut(t) {\n    const f = t - 1.0;\n    return f * f * f + 1.0;\n}\nfunction elasticInOut(t) {\n    return t < 0.5\n        ? 0.5 *\n            Math.sin(((+13.0 * Math.PI) / 2) * 2.0 * t) *\n            Math.pow(2.0, 10.0 * (2.0 * t - 1.0))\n        : 0.5 *\n            Math.sin(((-13.0 * Math.PI) / 2) * (2.0 * t - 1.0 + 1.0)) *\n            Math.pow(2.0, -10.0 * (2.0 * t - 1.0)) +\n            1.0;\n}\nfunction elasticIn(t) {\n    return Math.sin((13.0 * t * Math.PI) / 2) * Math.pow(2.0, 10.0 * (t - 1.0));\n}\nfunction elasticOut(t) {\n    return (Math.sin((-13.0 * (t + 1.0) * Math.PI) / 2) * Math.pow(2.0, -10.0 * t) + 1.0);\n}\nfunction expoInOut(t) {\n    return t === 0.0 || t === 1.0\n        ? t\n        : t < 0.5\n            ? +0.5 * Math.pow(2.0, 20.0 * t - 10.0)\n            : -0.5 * Math.pow(2.0, 10.0 - t * 20.0) + 1.0;\n}\nfunction expoIn(t) {\n    return t === 0.0 ? t : Math.pow(2.0, 10.0 * (t - 1.0));\n}\nfunction expoOut(t) {\n    return t === 1.0 ? t : 1.0 - Math.pow(2.0, -10.0 * t);\n}\nfunction quadInOut(t) {\n    t /= 0.5;\n    if (t < 1)\n        return 0.5 * t * t;\n    t--;\n    return -0.5 * (t * (t - 2) - 1);\n}\nfunction quadIn(t) {\n    return t * t;\n}\nfunction quadOut(t) {\n    return -t * (t - 2.0);\n}\nfunction quartInOut(t) {\n    return t < 0.5\n        ? +8.0 * Math.pow(t, 4.0)\n        : -8.0 * Math.pow(t - 1.0, 4.0) + 1.0;\n}\nfunction quartIn(t) {\n    return Math.pow(t, 4.0);\n}\nfunction quartOut(t) {\n    return Math.pow(t - 1.0, 3.0) * (1.0 - t) + 1.0;\n}\nfunction quintInOut(t) {\n    if ((t *= 2) < 1)\n        return 0.5 * t * t * t * t * t;\n    return 0.5 * ((t -= 2) * t * t * t * t + 2);\n}\nfunction quintIn(t) {\n    return t * t * t * t * t;\n}\nfunction quintOut(t) {\n    return --t * t * t * t * t + 1;\n}\nfunction sineInOut(t) {\n    return -0.5 * (Math.cos(Math.PI * t) - 1);\n}\nfunction sineIn(t) {\n    const v = Math.cos(t * Math.PI * 0.5);\n    if (Math.abs(v) < 1e-14)\n        return 1;\n    else\n        return 1 - v;\n}\nfunction sineOut(t) {\n    return Math.sin((t * Math.PI) / 2);\n}\n\nexport { backIn, backInOut, backOut, bounceIn, bounceInOut, bounceOut, circIn, circInOut, circOut, cubicIn, cubicInOut, cubicOut, elasticIn, elasticInOut, elasticOut, expoIn, expoInOut, expoOut, quadIn, quadInOut, quadOut, quartIn, quartInOut, quartOut, quintIn, quintInOut, quintOut, sineIn, sineInOut, sineOut };\n"], "mappings": ";AAMA,SAAS,UAAU,GAAG;AAClB,QAAM,IAAI,UAAU;AACpB,OAAK,KAAK,KAAK;AACX,WAAO,OAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AACzC,SAAO,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrD;AACA,SAAS,OAAO,GAAG;AACf,QAAM,IAAI;AACV,SAAO,IAAI,MAAM,IAAI,KAAK,IAAI;AAClC;AACA,SAAS,QAAQ,GAAG;AAChB,QAAM,IAAI;AACV,SAAO,EAAE,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK;AACzC;AACA,SAAS,UAAU,GAAG;AAClB,QAAM,IAAI,IAAM;AAChB,QAAM,IAAI,IAAM;AAChB,QAAM,IAAI,IAAM;AAChB,QAAM,KAAK,OAAS;AACpB,QAAM,KAAK,QAAU;AACrB,QAAM,KAAK,QAAU;AACrB,QAAM,KAAK,IAAI;AACf,SAAO,IAAI,IACL,SAAS,KACT,IAAI,IACA,QAAQ,KAAK,MAAM,IAAI,MACvB,IAAI,IACA,KAAK,KAAK,KAAK,IAAI,KACnB,OAAO,IAAI,IAAI,QAAQ,IAAI;AAC7C;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,IAAI,MACL,OAAO,IAAM,UAAU,IAAM,IAAI,CAAG,KACpC,MAAM,UAAU,IAAI,IAAM,CAAG,IAAI;AAC3C;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,IAAM,UAAU,IAAM,CAAC;AAClC;AACA,SAAS,UAAU,GAAG;AAClB,OAAK,KAAK,KAAK;AACX,WAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI;AAC1C,SAAO,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI;AAChD;AACA,SAAS,OAAO,GAAG;AACf,SAAO,IAAM,KAAK,KAAK,IAAM,IAAI,CAAC;AACtC;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,KAAK,KAAK,IAAI,EAAE,IAAI,CAAC;AAChC;AACA,SAAS,WAAW,GAAG;AACnB,SAAO,IAAI,MAAM,IAAM,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,IAAM,IAAI,GAAK,CAAG,IAAI;AAC5E;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,IAAI,IAAI;AACnB;AACA,SAAS,SAAS,GAAG;AACjB,QAAM,IAAI,IAAI;AACd,SAAO,IAAI,IAAI,IAAI;AACvB;AACA,SAAS,aAAa,GAAG;AACrB,SAAO,IAAI,MACL,MACE,KAAK,IAAM,KAAQ,KAAK,KAAM,IAAK,IAAM,CAAC,IAC1C,KAAK,IAAI,GAAK,MAAQ,IAAM,IAAI,EAAI,IACtC,MACE,KAAK,IAAM,MAAQ,KAAK,KAAM,KAAM,IAAM,IAAI,IAAM,EAAI,IACxD,KAAK,IAAI,GAAK,OAAS,IAAM,IAAI,EAAI,IACrC;AACZ;AACA,SAAS,UAAU,GAAG;AAClB,SAAO,KAAK,IAAK,KAAO,IAAI,KAAK,KAAM,CAAC,IAAI,KAAK,IAAI,GAAK,MAAQ,IAAI,EAAI;AAC9E;AACA,SAAS,WAAW,GAAG;AACnB,SAAQ,KAAK,IAAK,OAAS,IAAI,KAAO,KAAK,KAAM,CAAC,IAAI,KAAK,IAAI,GAAK,MAAQ,CAAC,IAAI;AACrF;AACA,SAAS,UAAU,GAAG;AAClB,SAAO,MAAM,KAAO,MAAM,IACpB,IACA,IAAI,MACA,MAAO,KAAK,IAAI,GAAK,KAAO,IAAI,EAAI,IACpC,OAAO,KAAK,IAAI,GAAK,KAAO,IAAI,EAAI,IAAI;AACtD;AACA,SAAS,OAAO,GAAG;AACf,SAAO,MAAM,IAAM,IAAI,KAAK,IAAI,GAAK,MAAQ,IAAI,EAAI;AACzD;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,MAAM,IAAM,IAAI,IAAM,KAAK,IAAI,GAAK,MAAQ,CAAC;AACxD;AACA,SAAS,UAAU,GAAG;AAClB,OAAK;AACL,MAAI,IAAI;AACJ,WAAO,MAAM,IAAI;AACrB;AACA,SAAO,QAAQ,KAAK,IAAI,KAAK;AACjC;AACA,SAAS,OAAO,GAAG;AACf,SAAO,IAAI;AACf;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,CAAC,KAAK,IAAI;AACrB;AACA,SAAS,WAAW,GAAG;AACnB,SAAO,IAAI,MACL,IAAO,KAAK,IAAI,GAAG,CAAG,IACtB,KAAO,KAAK,IAAI,IAAI,GAAK,CAAG,IAAI;AAC1C;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,KAAK,IAAI,GAAG,CAAG;AAC1B;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,KAAK,IAAI,IAAI,GAAK,CAAG,KAAK,IAAM,KAAK;AAChD;AACA,SAAS,WAAW,GAAG;AACnB,OAAK,KAAK,KAAK;AACX,WAAO,MAAM,IAAI,IAAI,IAAI,IAAI;AACjC,SAAO,QAAQ,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;AAC7C;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,IAAI,IAAI,IAAI,IAAI;AAC3B;AACA,SAAS,SAAS,GAAG;AACjB,SAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI;AACjC;AACA,SAAS,UAAU,GAAG;AAClB,SAAO,QAAQ,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI;AAC3C;AACA,SAAS,OAAO,GAAG;AACf,QAAM,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG;AACpC,MAAI,KAAK,IAAI,CAAC,IAAI;AACd,WAAO;AAAA;AAEP,WAAO,IAAI;AACnB;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,KAAK,IAAK,IAAI,KAAK,KAAM,CAAC;AACrC;", "names": []}