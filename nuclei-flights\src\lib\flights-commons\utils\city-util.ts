import { NucleiLogger } from '@CDNA-Technologies/svelte-vitals/logger';
import { dateUtils } from './date-util';

// Utility object to handle storing and retrieving all flight search data
export const cityUtils = {
	// Retrieves initial flight search data including cities, date, class, and travellers
	getInitialFlightData() {
		if (typeof window !== 'undefined') {
			// Try to get previously selected flight data from sessionStorage
			const stored = sessionStorage.getItem('flightSearchData');
			if (stored) {
				try {
					const parsedData = JSON.parse(stored);
					
					// Validate and return the stored data
					if (parsedData.cities && parsedData.searchParams) {
						return parsedData;
					}
				} catch (e) {
					NucleiLogger.logException("CITY-UTILS", 'Error parsing stored flight data:', e);
				}
			}
		}

		// Default values if nothing stored
		const today = new Date();
		return {
			cities: {
				from: {
					city: 'Bangalore',
					code: 'BLR',
					airport: 'Bangalore International Airport'
				},
				to: {
					city: 'New Delhi',
					code: 'DEL',
					airport: 'Indira Gandhi International Airport'
				}
			},
			searchParams: {
				departureDate: dateUtils.formatDate(today),
				departureDay: dateUtils.formatDay(today),
				selectedClass: 'Business Class',
				travellers: '01',
				adults: 1,
				children: 0,
				infants: 0,
				isReturnTrip: false,
				returnDate: '',
				showNonStop: true,
				selectedSpecialFare: 'Student'
			}
		};
	},

	// For backward compatibility - returns just the cities
	getInitialCities() {
		const flightData = this.getInitialFlightData();
		return flightData.cities;
	},

	// Save complete flight search data
	saveFlightSearchData(cities: any, searchParams: any) {
		if (typeof window !== 'undefined') {
			const dataToStore = {
				cities: cities,
				searchParams: searchParams,
				timestamp: new Date().getTime() // Optional: for data freshness check
			};
			
			try {
				sessionStorage.setItem('flightSearchData', JSON.stringify(dataToStore));
				NucleiLogger.logInfo("CITY-UTILS", 'Flight search data saved successfully');
			} catch (e) {
				NucleiLogger.logException("CITY-UTILS", 'Error saving flight search data:', e);
			}
		}
	},

	// For backward compatibility - saves only cities
	saveSelectedCities(selectedCities: any) {
		// Get existing data to preserve search params
		const existingData = this.getInitialFlightData();
		this.saveFlightSearchData(selectedCities, existingData.searchParams);
	},

	// Save only search parameters (keeping cities intact)
	saveSearchParams(searchParams: any) {
		const existingData = this.getInitialFlightData();
		this.saveFlightSearchData(existingData.cities, searchParams);
	},

	// Clear stored flight data
	clearFlightData() {
		if (typeof window !== 'undefined') {
			sessionStorage.removeItem('flightSearchData');
			// Also remove old key for backward compatibility
			sessionStorage.removeItem('flightSelectedCities');
		}
	}
};